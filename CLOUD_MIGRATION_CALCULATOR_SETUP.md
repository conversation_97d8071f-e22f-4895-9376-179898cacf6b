# Cloud Migration Cost Calculator - Setup Guide

## Overview
The Cloud Migration Cost Calculator is implemented following the same architectural patterns as the AI Readiness assessment. It provides a comprehensive multi-step form that calculates cloud migration costs based on user inputs and displays personalized results.

## Implementation Status ✅

### Completed Components:
1. **Main Page**: `/cloud-migration-cost-calculator/page.tsx`
2. **Calculator Body**: `CloudMigrationCalculatorBody` component
3. **Progress Step**: `CloudMigrationStep` component  
4. **Contact Form**: `CloudMigrationForm` component
5. **API Route**: `/api/cloud-migration-calculator/route.ts`
6. **Form Hook**: Updated `useForm.ts` with `handleSubmitCloudMigration`

## Required Environment Variables

Add these environment variables to your `.env.local` file:

```env
# HubSpot Configuration for Cloud Migration Calculator
NEXT_PUBLIC_HUBSPOT_CLOUD_MIGRATION_FORM_GUID=your_hubspot_form_guid_here

# SendGrid Template for Cloud Migration Calculator  
NEXT_PUBLIC_SENDGRID_CLOUD_MIGRATION_FORM_TEMPLATE_ID=your_sendgrid_template_id_here

# Existing variables (already configured)
NEXT_PUBLIC_HUBSPOT_API_KEY=your_hubspot_api_key
NEXT_PUBLIC_MAIL_TO=<EMAIL>
NEXT_PUBLIC_MAIL_FROM=<EMAIL>
NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL=your_slack_success_webhook
NEXT_PUBLIC_SLACK_ERROR_WEBHOOK_URL=your_slack_error_webhook
NEXT_PUBLIC_SITE_URL=https://yourdomain.com
```

## Strapi Content Structure

The following content types are already created in your Strapi instance (as shown in screenshots):

### 1. Cloud Migration Cost Calculator (Main Content Type)
- `hero_section` (Component)
- `cloud_migration_components` (Relation)
- `form` (Component) 
- `restart_button` (Component)
- `consultation_button` (Component)
- `cost_range_heading` (Text)
- `seo` (Component)

### 2. Cloud Migration Component
- `heading` (Text)
- `section_cost` (Number) - Base cost for this section
- `questions` (Component - repeatable)

### 3. Questions Component  
- `name` (Text)
- `type` (Enumeration: mcq, checkbox, range)
- `answers` (Component - repeatable)

### 4. Answers Component
- `name` (Text) 
- `value` (Number) - Cost value or multiplier

## Cost Calculation Logic

The calculator implements the spreadsheet logic with these cost factors:

### Server Count Costs:
- < 10 servers: $10,000
- 10-50 servers: $50,000  
- 50-100 servers: $250,000
- > 100 servers: $500,000

### Storage Capacity Costs:
- 10GB-50GB: $100
- 50GB-200GB: $500
- 200GB-1TB: $2,000
- 1TB-10TB: $10,000
- 10TB-50TB: $50,000
- > 50TB: $150,000

### Environment Costs:
- Development: $10,000
- Testing: $15,000
- Staging: $20,000
- Production: $100,000

### Compliance Costs:
- HIPAA: $20,000
- GDPR: $10,000
- PCI DSS: $15,000
- SOC 2: $15,000
- CCPA: $5,000
- FedRAMP: $50,000

### Migration Strategy Costs:
- Lift-and-shift: $5,000
- Re-platforming: $50,000
- Re-architecting: $150,000
- Hybrid approach: $100,000

### Additional Features:
- High Availability: +20% of infrastructure costs
- Auto-scaling: $10,000

## Sample Strapi Data Structure

```json
{
  "hero_section": {
    "title": "Cloud Migration Cost Calculator",
    "description": "Get an accurate estimate of your cloud migration costs",
    "image": "hero-image.jpg",
    "mobile_image": "hero-mobile.jpg",
    "button_title": "Start Assessment",
    "button_link": "#calculator"
  },
  "cloud_migration_components": [
    {
      "heading": "Business & Infrastructure Assessment",
      "section_cost": 0,
      "questions": [
        {
          "name": "Which elements are you planning to migrate to the cloud?",
          "type": "mcq",
          "answers": [
            {"name": "All infrastructure components", "value": 1},
            {"name": "Data storages", "value": 2},
            {"name": "Applications", "value": 3}
          ]
        }
      ]
    }
  ]
}
```

## Features Implemented

### ✅ Multi-step Form Navigation
- Progress indicator with percentage completion
- Previous/Next navigation
- Form validation with error handling
- Local storage persistence

### ✅ Cost Calculation Engine  
- Real-time cost calculation based on spreadsheet logic
- Cost range display (base + 30% upper range)
- Detailed cost breakdown by category

### ✅ Results Display
- Professional cost estimate presentation
- Benefits section with value propositions
- Call-to-action for consultation

### ✅ Form Integration
- HubSpot CRM integration
- SendGrid email notifications
- Slack notifications for success/error
- User tracking and analytics data

### ✅ Responsive Design
- Mobile-first approach
- Tablet and desktop breakpoints
- Consistent with existing design system

## Next Steps

1. **Configure Environment Variables**: Add the required HubSpot and SendGrid IDs
2. **Test Integration**: Verify HubSpot form submission and email templates
3. **Content Population**: Add questions and answers in Strapi following the cost calculation logic
4. **SEO Configuration**: Add meta tags and schema markup in Strapi
5. **Analytics Setup**: Ensure tracking pixels and conversion events are configured

## File Structure

```
src/
├── app/
│   ├── cloud-migration-cost-calculator/
│   │   └── page.tsx
│   └── api/
│       └── cloud-migration-calculator/
│           └── route.ts
├── components/
│   ├── CloudMigrationCalculatorBody/
│   │   ├── CloudMigrationCalculatorBody.tsx
│   │   ├── CloudMigrationCalculatorBody.module.css
│   │   └── index.ts
│   ├── CloudMigrationStep/
│   │   ├── CloudMigrationStep.tsx
│   │   ├── CloudMigrationStep.module.css
│   │   └── index.ts
│   └── CloudMigrationForm/
│       ├── CloudMigrationForm.tsx
│       ├── CloudMigrationForm.module.css
│       └── index.ts
└── hooks/
    └── useForm.ts (updated with handleSubmitCloudMigration)
```

The implementation is complete and ready for testing once the environment variables are configured!
