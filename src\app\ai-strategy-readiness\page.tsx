import React from 'react';
import seoSchema from '@utils/seoSchema';
import HeroSection from '@components/HeroSection';
import CTA from '@components/CTA';
import RichResults from '@components/RichResults';
import { notFound } from 'next/navigation';
import ContactUsForm from '@components/ContactUsForm';
import Testimonial from '@components/Testimonial';
import AuditMethodology from '@components/AuditMethodology';
import PodcastsSeries from '@components/PodcastsSeries';
import Deliverables from '@components/Deliverables';
import TabChallenges from '@components/TabChallenges';
import TitleDescription from '@components/TitleDescription';
import fetchFromStrapi from '@utils/fetchFromStrapi';
import Faq from '@components/Faq';
import TechStack from '@components/TechStack';
import WhyChooseMTL from '@components/WhyChooseMTL';

export default async function AiStrategyReadinessPage() {
  const queryString = `filters[slug][$eq]=ai-strategy-readiness&populate=hero_section.image,hero_section.mobile_image,solution,solution_with_video.podcast_episode.video_thumbnail_image,cta,challenges.box.image,our_audit_methodology.box,deliverables.box,seo.schema,scope_and_deliverables.box,our_audit_methodology.box,audit_button,why_choose_mtl.whyChooseMtlCards,tech_stack.tab.logo_url,faq.faq_items`;
  const response = await fetchFromStrapi(
    'ai-strategy-readinesses',
    queryString,
  );
  const data = response?.data?.[0]?.attributes;
  if (!data) return notFound();

  // Render your page using the fetched data
  return (
    <main>
      {/* Example: <HeroSection data={data.hero_section} /> */}
      <pre>{JSON.stringify(data, null, 2)}</pre>
    </main>
  );
}
